import {
  IsUUID,
  IsNotEmpty,
  IsString,
  IsDate,
  IsArray,
  IsNumber,
  IsPositive,
  ValidateNested,
  Matches,
  IsOptional,
  IsEnum,
  IsObject,
} from 'class-validator';
import { Type } from 'class-transformer';
import { RecordStatus } from '@common/enums';
import { StatsConfigDto } from '@business-base/application/dto/customer-preferences.dto';

export class TaxRules {
  @IsString()
  @IsOptional()
  penaltyFee?: string;

  @IsString()
  @IsOptional()
  dailyFee?: string;

  constructor(data?: Partial<TaxRules>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class CustomImportConfig {
  @IsString()
  @IsOptional()
  delimiter?: string;

  @ValidateNested()
  @Type(() => TaxRules)
  @IsOptional()
  taxRules?: TaxRules;

  @IsObject()
  @IsOptional()
  headerMapping?: Record<string, string>;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  additionalHeaders?: string[];

  constructor(data?: Partial<CustomImportConfig>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class ImportTransformer {
  @IsString()
  @IsOptional()
  name?: string;

  @IsNumber()
  @IsOptional()
  tax?: number;

  @IsNumber()
  @IsOptional()
  fee?: number;

  constructor(data?: Partial<ImportTransformer>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class PortfolioPreferences {
  @IsUUID('4')
  @IsOptional()
  defaultWorkflowId?: string;

  @IsString()
  @Matches(/^[+-]?\d+(\.\d+)?$/, {
    message: 'timezoneUTC must be a valid timezone offset (e.g., "-3", "+5.5")',
  })
  @IsOptional()
  timezoneUTC?: string;

  @IsString()
  @IsOptional()
  importCronExpression?: string;

  @IsUUID('4')
  @IsOptional()
  followUpWorkflowId?: string;

  @IsString()
  @IsOptional()
  followUpCronExpression?: string;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  followUpQuantity?: number;

  @IsNumber()
  @IsPositive()
  @IsOptional()
  followUpIntervalMinutes?: number;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  exportColumns?: string[];

  @ValidateNested()
  @Type(() => CustomImportConfig)
  @IsOptional()
  customImportConfig?: CustomImportConfig;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => StatsConfigDto)
  @IsOptional()
  statsConfig?: StatsConfigDto[];

  @IsArray()
  @IsOptional()
  exportConfig?: any[];

  constructor(data?: Partial<PortfolioPreferences>) {
    if (data) {
      Object.assign(this, data);
    }
  }
}

export class CustomerPreferencesEntity {
  @IsUUID('4')
  @IsOptional()
  customerId?: string;

  @ValidateNested()
  @Type(() => PortfolioPreferences)
  @IsOptional()
  portfolio?: PortfolioPreferences;

  @IsString()
  @IsOptional()
  test?: string;

  @IsEnum(RecordStatus)
  @IsNotEmpty()
  status: RecordStatus;

  @IsDate()
  @IsNotEmpty()
  readonly createdAt: Date;

  @IsDate()
  @IsNotEmpty()
  readonly updatedAt: Date;

  constructor(data: Partial<CustomerPreferencesEntity>) {
    // Set required defaults
    this.status = data.status || RecordStatus.ACTIVE;
    this.createdAt = data.createdAt || new Date();
    this.updatedAt = data.updatedAt || new Date();

    // Assign all properties dynamically
    Object.assign(this, data);
  }
}
