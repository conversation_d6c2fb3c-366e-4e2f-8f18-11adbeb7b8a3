import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Version,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { CustomerPreferencesDto } from '@business-base/application/dto/customer-preferences.dto';
import { CustomerPreferencesUseCase } from '@business-base/application/use-cases/customer-preferences.use-case';
import { CorrelationContextService } from '@common/services/correlation-context.service';
import { logger } from '@edutalent/commons-sdk';
import { AccountRoles } from '@common/auth/decorators/account-role.decorator';
import { UserRolesInAccount } from '@common/auth/decorators/user-role-in-account.decorator';
import { UserRoleInAccount, AccountRole } from '@common/enums';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';

@ApiTags('Customer Preferences')
@ApiBearerAuth()
@UserRolesInAccount(UserRoleInAccount.ADMIN)
@AccountRoles(AccountRole.BASIC)
@Controller('business-base/customer-preferences')
export class CustomerPreferencesController {
  constructor(private readonly customerPreferencesUseCase: CustomerPreferencesUseCase) { }

  @ApiOperation({
    summary: 'Create customer preferences',
    description: `
    Creates new preferences for a customer including portfolio settings, timezone configuration,
    cron expressions for import and follow-up scheduling, and export column preferences.

    **Strict Validation**: Only properties explicitly defined in the DTO schema are accepted.
    Any unknown or unmapped properties will be rejected with a 400 Bad Request error.

    The preferences include:
    - Default workflow configuration for portfolio processing
    - Timezone settings for scheduling operations
    - Cron expressions for automated import and follow-up tasks
    - Follow-up quantity and interval settings
    - Export column preferences for data exports
    `,
  })
  @ApiParam({
    name: 'customerId',
    description: 'UUID of the customer to create preferences for',
    example: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
    format: 'uuid',
  })
  @ApiBody({
    type: CustomerPreferencesDto,
    description: 'Customer preferences data to create',
    examples: {
      example: {
        summary: 'Complete customer preferences',
        value: {
          portfolio: {
            defaultWorkflowId: '6f413811-4aa8-43f4-8c48-d00143dd226d',
            timezoneUTC: '-3',
            importCronExpression: '0 9 * * 1-5',
            followUpWorkflowId: '7f413811-4aa8-43f4-8c48-d00143dd226e',
            followUpCronExpression: '0 */2 * * *',
            followUpQuantity: 3,
            followUpIntervalMinutes: 120,
            exportColumns: ['name', 'phone', 'status', 'lastInteraction', 'followUpCount'],
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Customer preferences created successfully',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 201 },
        data: {
          type: 'object',
          properties: {
            customerId: {
              type: 'string',
              format: 'uuid',
              example: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
            },
            portfolio: {
              type: 'object',
              properties: {
                defaultWorkflowId: { type: 'string', format: 'uuid' },
                timezoneUTC: { type: 'string', example: '-3' },
                importCronExpression: { type: 'string', example: '0 9 * * 1-5' },
                followUpWorkflowId: { type: 'string', format: 'uuid' },
                followUpCronExpression: { type: 'string', example: '0 */2 * * *' },
                followUpQuantity: { type: 'number', example: 3 },
                followUpIntervalMinutes: { type: 'number', example: 120 },
                exportColumns: { type: 'array', items: { type: 'string' } },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'array',
          items: { type: 'string' },
          example: [
            'portfolio.defaultWorkflowId must be a valid UUID',
            'portfolio.timezoneUTC must be a valid timezone offset (e.g., "-3", "+5.5")',
            'portfolio.importCronExpression must be a valid cron expression with exactly 5 fields (minute hour day month weekday)',
            'portfolio.exportConfig.0.workflowName must be a string',
          ],
        },
        error: { type: 'string', example: 'Bad Request' },
        statusCode: { type: 'number', example: 400 },
      },
    },
  })
  @ApiResponse({
    status: 409,
    description: 'Conflict - Customer preferences already exist',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Customer preferences already exist for this customer',
        },
        error: { type: 'string', example: 'Conflict' },
        statusCode: { type: 'number', example: 409 },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Internal server error occurred' },
        error: { type: 'string', example: 'Internal Server Error' },
        statusCode: { type: 'number', example: 500 },
      },
    },
  })
  @Post('/:customerId')
  @Version('1')
  @UsePipes(
    new ValidationPipe({
      whitelist: false, // Allow dynamic properties for exportConfig
      forbidNonWhitelisted: false, // Allow dynamic properties for exportConfig
      transform: true,
      transformOptions: { enableImplicitConversion: true },
    }),
  )
  async create(
    @Param('customerId') customerId: string,
    @Body() createCustomerPreferencesDto: CustomerPreferencesDto,
  ): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Creating customer preferences via HTTP', {
      traceId,
      customerId,
      operation: 'createCustomerPreferences',
      layer: 'CONTROLLER',
    });

    const customerPreferences = await this.customerPreferencesUseCase.create(
      customerId,
      createCustomerPreferencesDto,
    );

    logger.info('Customer preferences created successfully via HTTP', {
      traceId,
      customerId,
      operation: 'createCustomerPreferences',
      layer: 'CONTROLLER',
    });

    return {
      statusCode: 201,
      data: customerPreferences,
    };
  }

  @ApiOperation({
    summary: 'Get customer preferences by ID',
    description: `
    Retrieves the preferences for a specific customer including all portfolio settings,
    timezone configuration, cron expressions, and export preferences.

    Returns complete customer preferences data or 404 if not found.
    `,
  })
  @ApiParam({
    name: 'customerId',
    description: 'UUID of the customer to retrieve preferences for',
    example: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Customer preferences retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 200 },
        data: {
          type: 'object',
          properties: {
            customerId: {
              type: 'string',
              format: 'uuid',
              example: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
            },
            portfolio: {
              type: 'object',
              properties: {
                defaultWorkflowId: {
                  type: 'string',
                  format: 'uuid',
                  example: '6f413811-4aa8-43f4-8c48-d00143dd226d',
                },
                timezoneUTC: { type: 'string', example: '-3' },
                importCronExpression: { type: 'string', example: '0 9 * * 1-5' },
                followUpWorkflowId: {
                  type: 'string',
                  format: 'uuid',
                  example: '7f413811-4aa8-43f4-8c48-d00143dd226e',
                },
                followUpCronExpression: { type: 'string', example: '0 */2 * * *' },
                followUpQuantity: { type: 'number', example: 3 },
                followUpIntervalMinutes: { type: 'number', example: 120 },
                exportColumns: {
                  type: 'array',
                  items: { type: 'string' },
                  example: ['name', 'phone', 'status', 'lastInteraction', 'followUpCount'],
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Customer preferences not found',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example:
            'Customer preferences not found for customerId: 4cd6d515-2604-4c2c-adad-435acbef1f5c',
        },
        error: { type: 'string', example: 'Not Found' },
        statusCode: { type: 'number', example: 404 },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Internal server error occurred' },
        error: { type: 'string', example: 'Internal Server Error' },
        statusCode: { type: 'number', example: 500 },
      },
    },
  })
  @Get('/:customerId')
  @Version('1')
  async findById(@Param('customerId') customerId: string): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Finding customer preferences by ID via HTTP', {
      traceId,
      customerId,
      operation: 'findCustomerPreferencesById',
      layer: 'CONTROLLER',
    });

    const customerPreferences = await this.customerPreferencesUseCase.findById(customerId);

    logger.info('Customer preferences found successfully via HTTP', {
      traceId,
      customerId,
      operation: 'findCustomerPreferencesById',
      layer: 'CONTROLLER',
    });

    return {
      statusCode: 200,
      data: customerPreferences,
    };
  }

  @ApiOperation({
    summary: 'Update customer preferences',
    description: `
    Updates existing customer preferences with partial data. Only provided fields will be updated,
    existing values for omitted fields will be preserved.

    **Strict Validation**: Only properties explicitly defined in the DTO schema are accepted.
    Any unknown or unmapped properties will be rejected with a 400 Bad Request error.

    Supports partial updates for:
    - Portfolio workflow configurations
    - Timezone settings
    - Cron expressions for scheduling
    - Follow-up settings
    - Export column preferences

    The customer preferences must exist before updating.
    `,
  })
  @ApiParam({
    name: 'customerId',
    description: 'UUID of the customer to update preferences for',
    example: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
    format: 'uuid',
  })
  @ApiBody({
    type: CustomerPreferencesDto,
    description: 'Partial customer preferences data to update',
    examples: {
      partialUpdate: {
        summary: 'Partial update example',
        value: {
          portfolio: {
            timezoneUTC: '-5',
            followUpQuantity: 5,
            exportColumns: ['name', 'phone', 'status', 'lastInteraction', 'followUpCount'],
          },
        },
      },
      cronUpdate: {
        summary: 'Update cron expressions',
        value: {
          portfolio: {
            importCronExpression: '0 8 * * 1-6',
            followUpCronExpression: '0 */4 * * *',
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Customer preferences updated successfully',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 200 },
        data: {
          type: 'object',
          properties: {
            customerId: {
              type: 'string',
              format: 'uuid',
              example: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
            },
            portfolio: {
              type: 'object',
              properties: {
                defaultWorkflowId: { type: 'string', format: 'uuid' },
                timezoneUTC: { type: 'string', example: '-5' },
                importCronExpression: { type: 'string', example: '0 8 * * 1-6' },
                followUpWorkflowId: { type: 'string', format: 'uuid' },
                followUpCronExpression: { type: 'string', example: '0 */4 * * *' },
                followUpQuantity: { type: 'number', example: 5 },
                followUpIntervalMinutes: { type: 'number', example: 240 },
                exportColumns: { type: 'array', items: { type: 'string' } },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'array',
          items: { type: 'string' },
          example: [
            'portfolio.timezoneUTC must be a valid timezone offset (e.g., "-3", "+5.5")',
            'portfolio.followUpQuantity must be a positive number',
            'portfolio.exportConfig.0.workflowName must be a string',
          ],
        },
        error: { type: 'string', example: 'Bad Request' },
        statusCode: { type: 'number', example: 400 },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Customer preferences not found',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example:
            'Customer preferences not found for customerId: 4cd6d515-2604-4c2c-adad-435acbef1f5c',
        },
        error: { type: 'string', example: 'Not Found' },
        statusCode: { type: 'number', example: 404 },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Internal server error occurred' },
        error: { type: 'string', example: 'Internal Server Error' },
        statusCode: { type: 'number', example: 500 },
      },
    },
  })
  @Put('/:customerId')
  @Version('1')
  @UsePipes(
    new ValidationPipe({
      whitelist: false, // Allow dynamic properties for exportConfig
      forbidNonWhitelisted: false, // Allow dynamic properties for exportConfig
      transform: true,
      transformOptions: { enableImplicitConversion: true },
    }),
  )
  async update(
    @Param('customerId') customerId: string,
    @Body() updateCustomerPreferencesDto: CustomerPreferencesDto,
  ): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Updating customer preferences via HTTP', {
      traceId,
      customerId,
      operation: 'updateCustomerPreferences',
      layer: 'CONTROLLER',
    });

    const updatedCustomerPreferences = await this.customerPreferencesUseCase.update(
      customerId,
      updateCustomerPreferencesDto,
    );

    logger.info('Customer preferences updated successfully via HTTP', {
      traceId,
      customerId,
      operation: 'updateCustomerPreferences',
      layer: 'CONTROLLER',
    });

    return {
      statusCode: 200,
      data: updatedCustomerPreferences,
    };
  }

  @ApiOperation({
    summary: 'Delete customer preferences',
    description: `
    Permanently deletes all preferences for a specific customer including portfolio settings,
    timezone configuration, cron expressions, and export preferences.

    This action cannot be undone. The customer preferences must exist before deletion.

    After deletion, the customer will need to have preferences recreated if needed.
    `,
  })
  @ApiParam({
    name: 'customerId',
    description: 'UUID of the customer to delete preferences for',
    example: '4cd6d515-2604-4c2c-adad-435acbef1f5c',
    format: 'uuid',
  })
  @ApiResponse({
    status: 200,
    description: 'Customer preferences deleted successfully',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 200 },
        data: {
          type: 'object',
          properties: {
            message: { type: 'string', example: 'Customer preferences deleted successfully' },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Customer preferences not found',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example:
            'Customer preferences not found for customerId: 4cd6d515-2604-4c2c-adad-435acbef1f5c',
        },
        error: { type: 'string', example: 'Not Found' },
        statusCode: { type: 'number', example: 404 },
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Internal server error occurred' },
        error: { type: 'string', example: 'Internal Server Error' },
        statusCode: { type: 'number', example: 500 },
      },
    },
  })
  @Delete('/:customerId')
  @Version('1')
  async deleteById(@Param('customerId') customerId: string): Promise<any> {
    const traceId = CorrelationContextService.getTraceId();

    logger.info('Deleting customer preferences via HTTP', {
      traceId,
      customerId,
      operation: 'deleteCustomerPreferences',
      layer: 'CONTROLLER',
    });

    await this.customerPreferencesUseCase.delete(customerId);

    logger.info('Customer preferences deleted successfully via HTTP', {
      traceId,
      customerId,
      operation: 'deleteCustomerPreferences',
      layer: 'CONTROLLER',
    });

    return {
      statusCode: 200,
      data: { message: 'Customer preferences deleted successfully' },
    };
  }
}
