// Example demonstrating the refactored ExportConfigDto usage

import { ExportConfigDto, ExportFieldConfigDto } from './src/business-base/application/dto/customer-preferences.dto';

// Your provided ExportConfig[0] example
const exampleExportConfig: ExportConfigDto = {
  "Status": {
    path: "current_status",
    source: "portfolio-item",
  },
  "Nome da Distribuidora": {
    path: "NOME_DA_DISTRIBUIDORA",
    source: "customData",
  },
  "Número da Instalação": {
    path: "NUMERO_DA_INSTALACAO",
    source: "customData",
  },
  "Valor Original da Divida": {
    path: "VALOR_DIVIDA_ORIGINAL",
    source: "customData",
  },
  "Resumo da negociação": {
    path: "[resumo-negociacao].resumo",
    source: "middleware",
  },
  "workflowName": "digai-negociador-divida-workflow", // String value
  "Portfolio Name": {
    path: "name",
    source: "portfolio",
  },
  "Referencia da Divida": {
    path: "REFERENCIA_DIVIDA",
    source: "customData",
  },
  "Phone Number": {
    path: "phone_number",
    source: "portfolio-item",
  },
  "Nome do Cliente": {
    path: "NOME_DO_CLIENTE",
    source: "customData",
  },
  "CNPJ ou CPF do Cliente": {
    path: "CNPJ_CPF",
    source: "customData",
  },
  "Data do Acionamento": {
    format: "data_hora",
    path: "sent_at",
    source: "portfolio-item",
  },
};

// How the refactored code processes this:
function demonstrateUsage(exportConfig: ExportConfigDto) {
  // Extract all column names (both objects and strings)
  const columnNames = Object.keys(exportConfig);
  console.log('Column names:', columnNames);
  
  // Create CSV headers
  const headers = columnNames.map(columnName => ({ id: columnName, title: columnName }));
  console.log('CSV headers:', headers);
  
  // Process each column
  columnNames.forEach(columnName => {
    const fieldConfig = exportConfig[columnName];
    
    if (typeof fieldConfig === 'string') {
      console.log(`${columnName}: Direct string value = "${fieldConfig}"`);
    } else if (typeof fieldConfig === 'object' && fieldConfig !== null) {
      console.log(`${columnName}: Field config = ${JSON.stringify(fieldConfig)}`);
    }
  });
}

// Run the demonstration
demonstrateUsage(exampleExportConfig);

/*
Expected output:
Column names: [
  'Status', 'Nome da Distribuidora', 'Número da Instalação', 
  'Valor Original da Divida', 'Resumo da negociação', 'workflowName',
  'Portfolio Name', 'Referencia da Divida', 'Phone Number', 
  'Nome do Cliente', 'CNPJ ou CPF do Cliente', 'Data do Acionamento'
]

CSV headers: [
  { id: 'Status', title: 'Status' },
  { id: 'Nome da Distribuidora', title: 'Nome da Distribuidora' },
  // ... etc for all columns
]

Status: Field config = {"path":"current_status","source":"portfolio-item"}
Nome da Distribuidora: Field config = {"path":"NOME_DA_DISTRIBUIDORA","source":"customData"}
// ... etc for field configs
workflowName: Direct string value = "digai-negociador-divida-workflow"
// ... etc
*/
